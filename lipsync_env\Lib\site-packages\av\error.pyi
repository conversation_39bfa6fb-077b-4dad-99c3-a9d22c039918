import builtins
from enum import Enum

classes: dict[int, Exception]

def code_to_tag(code: int) -> bytes: ...
def tag_to_code(tag: bytes) -> int: ...
def err_check(res: int, filename: str | None = None) -> int: ...

class FFmpegError(Exception):
    errno: int | None
    strerror: str | None
    filename: str
    log: tuple[int, tuple[int, str, str] | None]

    def __init__(
        self,
        code: int,
        message: str,
        filename: str | None = None,
        log: tuple[int, tuple[int, str, str] | None] | None = None,
    ) -> None: ...

class LookupError(FFmpegError): ...
class HTTPError(FFmpegError): ...
class HTTPClientError(FFmpegError): ...
class UndefinedError(FFmpegError): ...
class InvalidDataError(FFmpegError, builtins.ValueError): ...
class BugError(FFmpegError, builtins.RuntimeError): ...
class BufferTooSmallError(FFmpegError, builtins.ValueError): ...
class BSFNotFoundError(LookupError): ...
class DecoderNotFoundError(LookupError): ...
class DemuxerNotFoundError(LookupError): ...
class EncoderNotFoundError(LookupError): ...
class ExitError(FFmpegError): ...
class ExternalError(FFmpegError): ...
class FilterNotFoundError(LookupError): ...
class MuxerNotFoundError(LookupError): ...
class OptionNotFoundError(LookupError): ...
class PatchWelcomeError(FFmpegError): ...
class ProtocolNotFoundError(LookupError): ...
class UnknownError(FFmpegError): ...
class ExperimentalError(FFmpegError): ...
class InputChangedError(FFmpegError): ...
class OutputChangedError(FFmpegError): ...
class HTTPBadRequestError(HTTPClientError): ...
class HTTPUnauthorizedError(HTTPClientError): ...
class HTTPForbiddenError(HTTPClientError): ...
class HTTPNotFoundError(HTTPClientError): ...
class HTTPOtherClientError(HTTPClientError): ...
class HTTPServerError(HTTPError): ...
class PyAVCallbackError(FFmpegError, builtins.RuntimeError): ...
class BrokenPipeError(FFmpegError, builtins.BrokenPipeError): ...
class ChildProcessError(FFmpegError, builtins.ChildProcessError): ...
class ConnectionAbortedError(FFmpegError, builtins.ConnectionAbortedError): ...
class ConnectionRefusedError(FFmpegError, builtins.ConnectionRefusedError): ...
class ConnectionResetError(FFmpegError, builtins.ConnectionResetError): ...
class BlockingIOError(FFmpegError, builtins.BlockingIOError): ...
class EOFError(FFmpegError, builtins.EOFError): ...
class FileExistsError(FFmpegError, builtins.FileExistsError): ...
class FileNotFoundError(FFmpegError, builtins.FileNotFoundError): ...
class InterruptedError(FFmpegError, builtins.InterruptedError): ...
class IsADirectoryError(FFmpegError, builtins.IsADirectoryError): ...
class MemoryError(FFmpegError, builtins.MemoryError): ...
class NotADirectoryError(FFmpegError, builtins.NotADirectoryError): ...
class NotImplementedError(FFmpegError, builtins.NotImplementedError): ...
class OverflowError(FFmpegError, builtins.OverflowError): ...
class OSError(FFmpegError, builtins.OSError): ...
class PermissionError(FFmpegError, builtins.PermissionError): ...
class ProcessLookupError(FFmpegError, builtins.ProcessLookupError): ...
class TimeoutError(FFmpegError, builtins.TimeoutError): ...
class ValueError(FFmpegError, builtins.ValueError): ...
